import User from "./User";
import Service from "./Service";
import Booking from "./Booking";
import BookingService from "./BookingService";
import Role from "./Role";
import UserRole from "./UserRole";
import Team from "./Team";
import TeamUser from "./TeamUser";
import Car from "./Car";

// Define associations
User.hasMany(Booking, { foreignKey: "userId" });
User.hasMany(Car, { foreignKey: "userId", as: "cars" });
Team.hasMany(Booking, { foreignKey: "teamId" });

// Booking associations
Booking.belongsTo(User, { foreignKey: "userId" });
Booking.belongsTo(Team, { foreignKey: "teamId" });

// Car associations
Car.belongsTo(User, { foreignKey: "userId", as: "owner" });

// Booking-Service many-to-many associations
Booking.belongsToMany(Service, {
    through: BookingService,
    foreignKey: "bookingId",
    otherKey: "serviceId",
    as: "services",
});

Service.belongsToMany(Booking, {
    through: BookingService,
    foreignKey: "serviceId",
    otherKey: "bookingId",
    as: "bookings",
});

// BookingService associations
BookingService.belongsTo(Booking, { foreignKey: "bookingId" });
BookingService.belongsTo(Service, { foreignKey: "serviceId" });
Booking.hasMany(BookingService, { foreignKey: "bookingId" });
Service.hasMany(BookingService, { foreignKey: "serviceId" });

// User-Role many-to-many associations
User.belongsToMany(Role, {
    through: UserRole,
    foreignKey: "userId",
    otherKey: "roleId",
    as: "roles",
});

Role.belongsToMany(User, {
    through: UserRole,
    foreignKey: "roleId",
    otherKey: "userId",
    as: "users",
});

// UserRole associations
UserRole.belongsTo(User, { foreignKey: "userId", as: "User" });
UserRole.belongsTo(Role, { foreignKey: "roleId", as: "Role" });
UserRole.belongsTo(User, { foreignKey: "assignedBy", as: "AssignedByUser" });

User.hasMany(UserRole, { foreignKey: "userId", as: "userRoles" });
Role.hasMany(UserRole, { foreignKey: "roleId", as: "roleUsers" });

// User-Team many-to-many associations
User.belongsToMany(Team, {
    through: TeamUser,
    foreignKey: "userId",
    otherKey: "teamId",
    as: "teams"
});

Team.belongsToMany(User, {
    through: TeamUser,
    foreignKey: "teamId",
    otherKey: "userId",
    as: "members"
});

// TeamUser associations
TeamUser.belongsTo(User, { foreignKey: "userId", as: "User" });
TeamUser.belongsTo(Team, { foreignKey: "teamId", as: "Team" });
TeamUser.belongsTo(User, { foreignKey: "addedBy", as: "AddedByUser" });

// Team leader association
Team.belongsTo(User, { foreignKey: "leaderId", as: "Leader" });

User.hasMany(TeamUser, { foreignKey: "userId", as: "teamMemberships" });
Team.hasMany(TeamUser, { foreignKey: "teamId", as: "teamMembers" });
User.hasMany(Team, { foreignKey: "leaderId", as: "leadingTeams" });

export { User, Service, Booking, BookingService, Role, UserRole, Team, TeamUser, Car };
