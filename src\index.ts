import express from "express";
import cors from "cors";
import dotenv from "dotenv";
import sequelize from "./config/database";
import routes from "./routes";
import { errorHandler, notFound } from "./middleware/errorHandler";
import rateLimiter from './config/rateLimiter';

// Load environment variables
dotenv.config();

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 8080;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(rateLimiter);

// Routes
app.use("/api", routes);

// Error handling middleware
app.use(notFound);
app.use(errorHandler);

// Database connection and server start
const startServer = async () => {
    try {
        // Test database connection
        await sequelize.authenticate();
        console.log("Database connection has been established successfully.");

        // Sync database models (in development)
        if (process.env.NODE_ENV === "development") {
            await sequelize.sync({ alter: true });
            console.log("Database models synchronized.");
        }

        // Start server
        app.listen(PORT, () => {
            console.log(`Server is running on port ${PORT}`);
        });
    } catch (error) {
        console.error("Unable to connect to the database:", error);
        process.exit(1);
    }
};

startServer();
