import express from "express";
import * as userController from "../controllers/userController";
import { authenticate, authorize } from "../middleware/auth";

const router = express.Router();

// router.get("/", authenticate, authorize(["admin"]), userController.getAllUsers);
router.get("/", userController.getAllUsers);
router.get("/:id", authenticate, userController.getUserById);
router.put("/:id", authenticate, userController.updateUser);
router.delete(
    "/:id",
    authenticate,
    authorize(["admin"]),
    userController.deleteUser
);

router.post("/:id/add-car", authenticate, userController.addCar);
router.get("/:id/list-cars", authenticate, userController.listCars);

export default router;
