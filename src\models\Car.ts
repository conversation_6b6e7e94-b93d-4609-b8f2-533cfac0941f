import { DataTypes, Optional } from "sequelize";
import sequelize from "../config/database";
import BaseModel from "./BaseModel";
import { User } from './index';
import { Op } from "sequelize";

interface CarAttributes {
    id: number;
    userId: number;
    make: string;
    model: string;
    year: number;
    type: string;
    color: string;
    licensePlate?: string;
    isMatte: boolean;
    isActive: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface CarCreationAttributes
    extends Optional<CarAttributes, "id" | "licensePlate" | "isActive" | "createdAt" | "updatedAt"> {}

class Car
    extends BaseModel<CarAttributes, CarCreationAttributes>
    implements CarAttributes
{
    public id!: number;
    public userId!: number;
    public make!: string;
    public model!: string;
    public year!: number;
    public type!: string;
    public color!: string;
    public licensePlate?: string;
    public isMatte!: boolean;
    public isActive!: boolean;
    public readonly createdAt!: Date;
    public readonly updatedAt!: Date;

    // Method to get car's full name
    public getFullName(): string {
        return `${this.year} ${this.make} ${this.model}`;
    }

    // Method to get car's display info
    public getDisplayInfo(): string {
        const matteText = this.isMatte ? " (Matte)" : "";
        const plateText = this.licensePlate ? ` - ${this.licensePlate}` : "";
        return `${this.getFullName()} - ${this.color}${matteText}${plateText}`;
    }

    // Method to check if car is vintage (older than 25 years)
    public isVintage(): boolean {
        const currentYear = new Date().getFullYear();
        return (currentYear - this.year) >= 25;
    }

    // Method to get car owner
    public async getOwner(): Promise<any> {
        return User.findById(this.userId);
    }

    // Static method to find cars by user
    static async findByUserId(userId: number): Promise<Car[]> {
        return this.findAll({
            where: { userId, isActive: true },
            order: [['createdAt', 'DESC']]
        });
    }

    // Static method to find car by license plate
    static async findByLicensePlate(licensePlate: string): Promise<Car | null> {
        return this.findOne({
            where: { licensePlate, isActive: true }
        });
    }

    // Static method to find cars by make and model
    static async findByMakeAndModel(make: string, model: string): Promise<Car[]> {
        return this.findAll({
            where: { make, model, isActive: true },
            order: [['year', 'DESC']]
        });
    }
}

Car.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        userId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE',
        },
        make: {
            type: DataTypes.STRING,
            allowNull: false,
            validate: {
                notEmpty: true,
                len: [1, 50]
            }
        },
        model: {
            type: DataTypes.STRING,
            allowNull: false,
            validate: {
                notEmpty: true,
                len: [1, 50]
            }
        },
        year: {
            type: DataTypes.INTEGER,
            allowNull: false,
            validate: {
                min: 1900,
                max: new Date().getFullYear() + 1, // Allow next year for pre-orders
                isInt: true
            }
        },
        type: {
            type: DataTypes.STRING,
            allowNull: false,
            validate: {
                notEmpty: true,
                isIn: [['sedan', 'suv', 'truck', 'coupe', 'convertible', 'hatchback', 'wagon', 'van', 'motorcycle', 'other']]
            }
        },
        color: {
            type: DataTypes.STRING,
            allowNull: false,
            validate: {
                notEmpty: true,
                len: [1, 30]
            }
        },
        licensePlate: {
            type: DataTypes.STRING,
            allowNull: true,
            validate: {
                len: [1, 20]
            }
        },
        isMatte: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
            comment: "True if the car has matte finish"
        },
        isActive: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true,
            comment: "True if the car is still owned/active"
        },
    },
    {
        sequelize,
        modelName: "Car",
        tableName: "cars",
        indexes: [
            {
                fields: ['userId']
            },
            {
                fields: ['make', 'model']
            },
            {
                fields: ['licensePlate'],
                unique: true,
                where: {
                    licensePlate: {
                        [Op.ne]: null
                    }
                }
            }
        ]
    }
);

export default Car;
