name: Deploy to Development

on:
  push:
    branches: [ develop ]
  pull_request:
    branches: [ develop ]

env:
  NODE_VERSION: '18'
  DOCKER_IMAGE: mobile-carwash
  DEPLOYMENT_PATH: /root/mobile_carwash_server

jobs:
  test:
    name: Test Application
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run TypeScript type check
      run: npx tsc --noEmit
      
    - name: Run tests
      run: npm test || echo "No tests configured yet"
      
    - name: Run security audit
      run: npm audit --audit-level high || true

  build:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Build Docker image
      run: |
        docker build \
          --no-cache \
          --target production \
          --tag ${{ env.DOCKER_IMAGE }}:dev-${{ github.sha }} \
          --tag ${{ env.DOCKER_IMAGE }}:dev-latest \
          --build-arg BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ') \
          --build-arg VCS_REF=${{ github.sha }} \
          .
          
    # - name: Test Docker image
    #   run: |
    #     # Test that the image can start
    #     docker run --rm \
    #       --name test-container \
    #       -e NODE_ENV=production \
    #       -e PORT=8080 \
    #       -e DB_URI="postgresql://test:test@localhost:5432/test" \
    #       -e JWT_SECRET="test-secret" \
    #       -e STRIPE_SECRET_KEY="sk_test_test" \
    #       -p 8080:8080 \
    #       ${{ env.DOCKER_IMAGE }}:dev-latest
          
    #     # Wait for container to start
    #     sleep 20

    #     docker logs test-container  # Debug logs

    #     # Check if container is running
    #     docker ps | grep test-container
        
    #     # Stop test container
    #     docker stop test-container
        
    - name: Save Docker image
      run: |
        docker save ${{ env.DOCKER_IMAGE }}:dev-${{ github.sha }} ${{ env.DOCKER_IMAGE }}:dev-latest | gzip > mobile-carwash-dev.tar.gz
        
    - name: Upload Docker image artifact
      uses: actions/upload-artifact@v4
      with:
        name: docker-image-dev
        path: mobile-carwash-dev.tar.gz
        retention-days: 1

  deploy:
    name: Deploy to Development Server
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    
    environment: development
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download Docker image artifact
      uses: actions/download-artifact@v4
      with:
        name: docker-image-dev
        
    - name: Setup SSH
      run: |
        mkdir -p ~/.ssh
        echo "${{ secrets.DEV_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh-keyscan -H ${{ secrets.DEV_SERVER_IP }} >> ~/.ssh/known_hosts
        
    - name: Copy files to server
      run: |
        # Copy Docker image
        scp -i ~/.ssh/id_rsa mobile-carwash-dev.tar.gz ${{ secrets.DEV_SERVER_USER }}@${{ secrets.DEV_SERVER_IP }}:/tmp/
        
        # Copy deployment docker-compose file
        scp -i ~/.ssh/id_rsa docker-compose.deploy.yml ${{ secrets.DEV_SERVER_USER }}@${{ secrets.DEV_SERVER_IP }}:/tmp/
        
    - name: Deploy to development server
      run: |
        ssh -i ~/.ssh/id_rsa ${{ secrets.DEV_SERVER_USER }}@${{ secrets.DEV_SERVER_IP }} << 'EOF'
          set -e
          
          # Navigate to deployment directory
          cd ${{ env.DEPLOYMENT_PATH }}
          
          # Create environment file
          sudo tee .env > /dev/null << 'ENVEOF'
        NODE_ENV=development
        PORT=8080
        DB_URI=${{ secrets.DEV_DB_URI }}
        JWT_SECRET=${{ secrets.DEV_JWT_SECRET }}
        JWT_EXPIRES_IN=7d
        STRIPE_SECRET_KEY=${{ secrets.DEV_STRIPE_SECRET_KEY }}
        STRIPE_PUBLISHABLE_KEY=${{ secrets.DEV_STRIPE_PUBLISHABLE_KEY }}
        STRIPE_CURRENCY=${{ secrets.DEV_STRIPE_CURRENCY }}
        ENVEOF
          
          # Stop existing services and remove containers completely
          sudo docker-compose -f docker-compose.deploy.yml down --volumes --remove-orphans || true

          # Create backup directory for image preservation
          mkdir -p backups

          # Save current image as backup before replacing (if it exists)
          CURRENT_IMAGE=$(sudo docker images mobile-carwash:latest --format '{{.ID}}' | head -1)
          if [ ! -z "$CURRENT_IMAGE" ]; then
            CURRENT_TAG=$(sudo docker images mobile-carwash --format '{{.Tag}}' | grep -v latest | head -1)
            if [ ! -z "$CURRENT_TAG" ]; then
              echo "💾 Backing up current image: mobile-carwash:$CURRENT_TAG"
              sudo docker save mobile-carwash:$CURRENT_TAG | gzip > backups/mobile-carwash-$CURRENT_TAG-$(date +%Y%m%d-%H%M%S).tar.gz
              echo "$CURRENT_TAG" > backups/last-version.txt
            fi
          fi

          # Clean up only dangling images (not tagged images)
          sudo docker image prune -f

          # Load new Docker image
          sudo docker load < /tmp/mobile-carwash-dev.tar.gz

          # Update docker-compose file (deployment version that uses images)
          sudo cp /tmp/docker-compose.deploy.yml .

          # Tag the new image as latest
          sudo docker tag mobile-carwash:dev-${{ github.sha }} mobile-carwash:latest

          # Verify the image was loaded and tagged correctly
          echo "Available images after loading:"
          sudo docker images mobile-carwash

          # Remove any existing containers with same name
          sudo docker rm -f mobile-carwash-api-cloud || true

          # Start services with --force-recreate to ensure everything is fresh
          sudo docker-compose -f docker-compose.deploy.yml up -d --force-recreate --renew-anon-volumes

          # Clean up
          rm -f /tmp/mobile-carwash-dev.tar.gz /tmp/docker-compose.deploy.yml
          
          # Wait for services to be ready
          echo "Waiting for services to start..."
          sleep 30
          
          # Check if services are running
          sudo docker-compose -f docker-compose.deploy.yml ps
        EOF
        
    - name: Health check
      run: |
        # Wait a bit more for services to fully start
        sleep 30
        
        # Run health check and verify deployment
        ssh -i ~/.ssh/id_rsa ${{ secrets.DEV_SERVER_USER }}@${{ secrets.DEV_SERVER_IP }} << 'EOF'
          cd /root/mobile_carwash_server

          # Check container status
          echo "🐳 Container status:"
          sudo docker-compose -f docker-compose.deploy.yml ps

          # Check which image is running
          echo "📦 Running image:"
          sudo docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}"

          # Test health endpoint
          echo "🏥 Testing health endpoint..."
          curl -f http://localhost:8080/api/health || exit 1

          # Verify the code version (check a recent file timestamp)
          echo "📅 Code verification:"
          sudo docker exec mobile-carwash-api-cloud ls -la /app/dist/ | head -5

          # Check recent logs for any errors
          echo "📋 Recent logs:"
          sudo docker-compose -f docker-compose.deploy.yml logs --tail=20 app

          # Record successful deployment
          mkdir -p backups
          echo "dev-${{ github.sha }}" > backups/last-known-good.txt
          echo "$(date): Successfully deployed dev-${{ github.sha }}" >> backups/deployment-history.txt

          echo "✅ Development deployment successful!"
        EOF
        
    - name: Cleanup old Docker images and backups
      run: |
        ssh -i ~/.ssh/id_rsa ${{ secrets.DEV_SERVER_USER }}@${{ secrets.DEV_SERVER_IP }} << 'EOF'
          cd ${{ env.DEPLOYMENT_PATH }}

          # Keep only the last 5 Docker images (excluding latest)
          echo "🧹 Cleaning up old Docker images..."
          OLD_IMAGES=$(sudo docker images mobile-carwash --format '{{.Tag}}' | grep -v latest | tail -n +6)
          if [ ! -z "$OLD_IMAGES" ]; then
            echo "$OLD_IMAGES" | while read tag; do
              echo "Removing old image: mobile-carwash:$tag"
              sudo docker rmi mobile-carwash:$tag || true
            done
          fi

          # Keep only the last 10 backup files
          if [ -d "backups" ]; then
            echo "🧹 Cleaning up old backup files..."
            ls -t backups/*.tar.gz 2>/dev/null | tail -n +11 | xargs -r rm -f || true
          fi

          # Clean up unused images and containers
          sudo docker image prune -f
          sudo docker container prune -f

          echo "📊 Current images after cleanup:"
          sudo docker images mobile-carwash
        EOF

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [test, build, deploy]
    if: always()
    
    steps:
    - name: Notify success
      if: needs.deploy.result == 'success'
      run: |
        echo "✅ Development deployment successful!"
        echo "🚀 Version: dev-${{ github.sha }}"
        echo "🌐 Environment: Development"
        echo "📦 Commit: ${{ github.event.head_commit.message }}"
        
    - name: Notify failure
      if: needs.deploy.result == 'failure' || needs.build.result == 'failure' || needs.test.result == 'failure'
      run: |
        echo "❌ Development deployment failed!"
        echo "🔍 Check the logs for details"
        echo "📦 Commit: ${{ github.event.head_commit.message }}"
