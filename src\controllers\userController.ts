import { Request, Response } from "express";
import { User, Car } from "../models";

export const getAllUsers = async (req: Request, res: Response) => {
    try {
        const users = await User.findAllPaginated({
            attributes: { exclude: ["password"] },
        });

        // // Get roles for each user
        // const usersWithRoles = await Promise.all(
        //     users.map(async (user) => {
        //         const roles = await user.getRoles();
        //         return {
        //             id: user.id,
        //             firstName: user.firstName,
        //             lastName: user.lastName,
        //             email: user.email,
        //             phone: user.phone,
        //             roles,
        //             createdAt: user.createdAt,
        //             updatedAt: user.updatedAt,
        //         };
        //     })
        // );

        res.status(200).send({
            users: users,
        });
    } catch (error) {
        console.error("Error fetching users:", error);
        res.status(500).send({ message: "Error fetching users" });
    }
};

export const getUserById = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const user = await User.findById(id, {
            attributes: { exclude: ["password"] },
        });

        if (!user) {
            return res.status(404).send({ message: "User not found" });
        }

        // Get user roles
        const roles = await user.getRoles();

        res.status(200).send({
            user: {
                id: user.id,
                firstName: user.firstName,
                lastName: user.lastName,
                email: user.email,
                phone: user.phone,
                roles,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt,
            },
        });
    } catch (error) {
        console.error("Error fetching user:", error);
        res.status(500).send({ message: "Error fetching user" });
    }
};

export const updateUser = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const { firstName, lastName, email, phone } = req.body;

        const user = await User.findById(id);

        if (!user) {
            return res.status(404).send({ message: "User not found" });
        }

        // Check if the authenticated user is updating their own profile or is an admin
        const isAdmin = req.user?.roles.includes("admin");
        const isOwnProfile = req.user?.id === user.id;

        if (!isOwnProfile && !isAdmin) {
            return res
                .status(403)
                .send({ message: "Not authorized to update this user" });
        }

        await user.update({
            firstName,
            lastName,
            email,
            phone,
        });

        // Get updated user roles
        const roles = await user.getRoles();

        res.status(200).send({
            message: "User updated successfully",
            user: {
                id: user.id,
                firstName: user.firstName,
                lastName: user.lastName,
                email: user.email,
                phone: user.phone,
                roles,
            },
        });
    } catch (error) {
        console.error("Error updating user:", error);
        res.status(500).send({ message: "Error updating user" });
    }
};

export const deleteUser = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;

        const user = await User.findById(id);

        if (!user) {
            return res.status(404).send({ message: "User not found" });
        }

        // Only admin can delete users
        const isAdmin = req.user?.roles.includes("admin");
        if (!isAdmin) {
            return res
                .status(403)
                .send({ message: "Not authorized to delete users" });
        }

        await user.destroy();

        res.status(200).send({ message: "User deleted successfully" });
    } catch (error) {
        console.error("Error deleting user:", error);
        res.status(500).send({ message: "Error deleting user" });
    }
};

export const addCar = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const { make, model, year, type, color, licensePlate, isMatte } =
            req.body;

        const user = await User.findById(id);
        if (!user) {
            return res.status(404).send({ message: "User not found" });
        }

        // Check if the authenticated user is updating their own profile or is an admin
        const isAdmin = req.user?.roles.includes("admin");
        const isOwnProfile = req.user?.id === user.id;

        if (!isOwnProfile && !isAdmin) {
            return res
                .status(403)
                .send({ message: "Not authorized to update this user" });
        }

        if (!make || !model || !year || !type || !color || !licensePlate) {
            return res.status(404).send({ message: "Missing props" });
        }

        const alreadyExistingCar = await Car.findByLicensePlate(licensePlate);
        if (alreadyExistingCar && alreadyExistingCar.userId === user.id) {
            return res.status(404).send({ message: "Already in your garage" });
        }

        const licensePlateReworked: string = licensePlate.replaceAll(" ", "");

        await user.addCar({
            color,
            make,
            model,
            type,
            year,
            isMatte: isMatte || false,
            licensePlate: licensePlateReworked.toLowerCase(),
        });

        res.status(200).send({
            message: "Successfully added car to your garage",
        });
    } catch (error) {
        console.error("Error creating car:", error);
        res.status(500).send({ message: "Error creating car" });
    }
};

export const listCars = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;

        const user = await User.findById(id);
        if (!user) {
            return res.status(404).send({ message: "User not found" });
        }

        // Check if the authenticated user is updating their own profile or is an admin
        const isAdmin = req.user?.roles.includes("admin");
        const isOwnProfile = req.user?.id === user.id;

        if (!isOwnProfile && !isAdmin) {
            return res
                .status(403)
                .send({ message: "Not authorized to list user cars" });
        }

        const cars = await user.getActiveCars();
        res.status(200).send({
            cars,
        });
    } catch (error) {
        console.error("Error listing cars:", error);
        res.status(500).send({ message: "Error listing cars" });
    }
};
